import React, { useState, useRef, useEffect } from 'react';
import { INSERT_TABLE_COMMAND } from '@lexical/table';
import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';

export function TableGridSelector({ isOpen, onClose, triggerRef }) {
  const [editor] = useLexicalComposerContext();
  const [hoveredCell, setHoveredCell] = useState({ row: 0, col: 0 });
  const [selectedSize, setSelectedSize] = useState({ rows: 1, cols: 1 });
  const gridRef = useRef(null);
  const maxRows = 10;
  const maxCols = 10;

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        gridRef.current &&
        !gridRef.current.contains(event.target) &&
        triggerRef.current &&
        !triggerRef.current.contains(event.target)
      ) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [isOpen, onClose, triggerRef]);

  const handleCellHover = (row, col) => {
    setHoveredCell({ row, col });
    setSelectedSize({ rows: row + 1, cols: col + 1 });
  };

  const handleCellClick = (row, col) => {
    const rows = row + 1;
    const cols = col + 1;
    
    editor.dispatchCommand(INSERT_TABLE_COMMAND, {
      columns: cols.toString(),
      rows: rows.toString(),
      includeHeaders: true
    });
    
    onClose();
  };

  const renderGrid = () => {
    const cells = [];
    for (let row = 0; row < maxRows; row++) {
      for (let col = 0; col < maxCols; col++) {
        const isSelected = row <= hoveredCell.row && col <= hoveredCell.col;
        cells.push(
          <div
            key={`${row}-${col}`}
            className={`table-grid-cell ${isSelected ? 'selected' : ''}`}
            onMouseEnter={() => handleCellHover(row, col)}
            onClick={() => handleCellClick(row, col)}
          />
        );
      }
    }
    return cells;
  };

  if (!isOpen) return null;

  return (
    <div className="table-grid-selector" ref={gridRef}>
      <div className="table-grid-header">
        <span className="table-grid-title">Insert Table</span>
        <span className="table-grid-size">
          {selectedSize.rows} × {selectedSize.cols} Table
        </span>
      </div>
      <div 
        className="table-grid"
        style={{
          gridTemplateColumns: `repeat(${maxCols}, 1fr)`,
          gridTemplateRows: `repeat(${maxRows}, 1fr)`
        }}
      >
        {renderGrid()}
      </div>
      <div className="table-grid-footer">
        <small>Hover to select table size, click to insert</small>
      </div>
    </div>
  );
}
