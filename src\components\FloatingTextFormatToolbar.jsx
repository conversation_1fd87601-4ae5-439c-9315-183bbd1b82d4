import { $isRangeSelection, $getSelection } from 'lexical';
import { $isLinkNode, TOGGLE_LINK_COMMAND } from '@lexical/link';
import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';
import { useCallback, useEffect, useRef, useState } from 'react';
import { Bold, Italic, Underline, Link, Strikethrough } from 'lucide-react';
import { FORMAT_TEXT_COMMAND } from 'lexical';

const LowPriority = 1;

function setFloatingElemPosition(
  targetRect,
  floatingElem,
  anchorElem,
) {
  const scrollerElem = anchorElem.parentElement;

  if (targetRect === null || !scrollerElem) {
    floatingElem.style.opacity = '0';
    floatingElem.style.transform = 'translate(-10000px, -10000px)';
    return;
  }

  const floatingElemRect = floatingElem.getBoundingClientRect();
  const anchorElementRect = anchorElem.getBoundingClientRect();
  const editorScrollerRect = scrollerElem.getBoundingClientRect();

  let top = targetRect.top - floatingElemRect.height + targetRect.height / 2;
  let left = targetRect.left - floatingElemRect.width / 2 + targetRect.width / 2;

  if (top < editorScrollerRect.top) {
    top += floatingElemRect.height + targetRect.height;
  }

  if (left < editorScrollerRect.left) {
    left = editorScrollerRect.left;
  }

  if (left + floatingElemRect.width > editorScrollerRect.right) {
    left = editorScrollerRect.right - floatingElemRect.width;
  }

  top -= anchorElementRect.top;
  left -= anchorElementRect.left;

  floatingElem.style.opacity = '1';
  floatingElem.style.transform = `translate(${left}px, ${top}px)`;
}

export function FloatingTextFormatToolbar() {
  const [editor] = useLexicalComposerContext();
  const [isText, setIsText] = useState(false);
  const [isLink, setIsLink] = useState(false);
  const [isBold, setIsBold] = useState(false);
  const [isItalic, setIsItalic] = useState(false);
  const [isUnderline, setIsUnderline] = useState(false);
  const [isStrikethrough, setIsStrikethrough] = useState(false);

  const popupCharStylesEditorRef = useRef(null);

  const updateTextFormatFloatingToolbar = useCallback(() => {
    const selection = $getSelection();

    const popupCharStylesEditorElem = popupCharStylesEditorRef.current;
    const nativeSelection = window.getSelection();

    if (popupCharStylesEditorElem === null) {
      return;
    }

    const rootElement = editor.getRootElement();
    if (
      selection !== null &&
      nativeSelection !== null &&
      !nativeSelection.isCollapsed &&
      rootElement !== null &&
      rootElement.contains(nativeSelection.anchorNode)
    ) {
      const rangeRect = nativeSelection.getRangeAt(0).getBoundingClientRect();
      setFloatingElemPosition(rangeRect, popupCharStylesEditorElem, rootElement);
    }
  }, [editor]);

  useEffect(() => {
    const updateToolbar = () => {
      const selection = $getSelection();
      if ($isRangeSelection(selection)) {
        // Update text format
        setIsBold(selection.hasFormat('bold'));
        setIsItalic(selection.hasFormat('italic'));
        setIsUnderline(selection.hasFormat('underline'));
        setIsStrikethrough(selection.hasFormat('strikethrough'));

        // Update link format
        const node = selection.anchor.getNode();
        const parent = node.getParent();
        if ($isLinkNode(parent) || $isLinkNode(node)) {
          setIsLink(true);
        } else {
          setIsLink(false);
        }

        const textContent = selection.getTextContent();
        if (textContent !== '') {
          setIsText(true);
        } else {
          setIsText(false);
        }
      } else {
        setIsText(false);
      }
    };

    return editor.registerUpdateListener(({ editorState }) => {
      editorState.read(() => {
        updateToolbar();
      });
    });
  }, [editor]);

  useEffect(() => {
    const updateToolbar = () => {
      updateTextFormatFloatingToolbar();
    };

    return editor.registerUpdateListener(({ editorState }) => {
      editorState.read(() => {
        updateToolbar();
      });
    });
  }, [editor, updateTextFormatFloatingToolbar]);

  const formatText = (format) => {
    editor.dispatchCommand(FORMAT_TEXT_COMMAND, format);
  };

  const insertLink = useCallback(() => {
    if (!isLink) {
      editor.dispatchCommand(TOGGLE_LINK_COMMAND, 'https://');
    } else {
      editor.dispatchCommand(TOGGLE_LINK_COMMAND, null);
    }
  }, [editor, isLink]);

  if (!isText) {
    return null;
  }

  return (
    <div ref={popupCharStylesEditorRef} className="floating-text-format-popup">
      <button
        onClick={() => formatText('bold')}
        className={`popup-item ${isBold ? 'active' : ''}`}
        aria-label="Format text as bold"
      >
        <Bold size={16} />
      </button>
      <button
        onClick={() => formatText('italic')}
        className={`popup-item ${isItalic ? 'active' : ''}`}
        aria-label="Format text as italic"
      >
        <Italic size={16} />
      </button>
      <button
        onClick={() => formatText('underline')}
        className={`popup-item ${isUnderline ? 'active' : ''}`}
        aria-label="Format text as underlined"
      >
        <Underline size={16} />
      </button>
      <button
        onClick={() => formatText('strikethrough')}
        className={`popup-item ${isStrikethrough ? 'active' : ''}`}
        aria-label="Format text as strikethrough"
      >
        <Strikethrough size={16} />
      </button>
      <button
        onClick={insertLink}
        className={`popup-item ${isLink ? 'active' : ''}`}
        aria-label="Insert link"
      >
        <Link size={16} />
      </button>
    </div>
  );
}