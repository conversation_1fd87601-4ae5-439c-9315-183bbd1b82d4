import { $isLinkNode, TOGGLE_LINK_COMMAND } from '@lexical/link';
import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';
import { $getSelection, $isRangeSelection } from 'lexical';
import { useCallback, useEffect, useRef, useState } from 'react';
import { Check, X, Edit, Trash2 } from 'lucide-react';

export function FloatingLinkEditor({ isLinkEditMode, setIsLinkEditMode }) {
  const [editor] = useLexicalComposerContext();
  const editorRef = useRef(null);
  const inputRef = useRef(null);
  const [linkUrl, setLinkUrl] = useState('');
  const [editedLinkUrl, setEditedLinkUrl] = useState('');
  const [isEditingLink, setIsEditingLink] = useState(false);

  const updateLinkEditor = useCallback(() => {
    const selection = $getSelection();
    if ($isRangeSelection(selection)) {
      const node = selection.anchor.getNode();
      const parent = node.getParent();
      if ($isLinkNode(parent)) {
        setLinkUrl(parent.getURL());
      } else if ($isLinkNode(node)) {
        setLinkUrl(node.getURL());
      } else {
        setLinkUrl('');
      }
    }
  }, []);

  useEffect(() => {
    return editor.registerUpdateListener(({ editorState }) => {
      editorState.read(() => {
        updateLinkEditor();
      });
    });
  }, [editor, updateLinkEditor]);

  useEffect(() => {
    if (isEditingLink && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isEditingLink]);

  const handleLinkSubmit = () => {
    if (editedLinkUrl !== '') {
      editor.dispatchCommand(TOGGLE_LINK_COMMAND, editedLinkUrl);
    }
    setIsEditingLink(false);
    setEditedLinkUrl('');
  };

  const handleLinkEdit = () => {
    setEditedLinkUrl(linkUrl);
    setIsEditingLink(true);
  };

  const handleLinkRemove = () => {
    editor.dispatchCommand(TOGGLE_LINK_COMMAND, null);
  };

  if (!linkUrl) {
    return null;
  }

  return (
    <div ref={editorRef} className="link-editor">
      {isEditingLink ? (
        <div className="link-input-wrapper">
          <input
            ref={inputRef}
            value={editedLinkUrl}
            onChange={(e) => setEditedLinkUrl(e.target.value)}
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                handleLinkSubmit();
              } else if (e.key === 'Escape') {
                setIsEditingLink(false);
                setEditedLinkUrl('');
              }
            }}
            className="link-input"
            placeholder="Enter URL"
          />
          <button onClick={handleLinkSubmit} className="link-btn">
            <Check size={16} />
          </button>
          <button
            onClick={() => {
              setIsEditingLink(false);
              setEditedLinkUrl('');
            }}
            className="link-btn"
          >
            <X size={16} />
          </button>
        </div>
      ) : (
        <div className="link-preview">
          <a
            href={linkUrl}
            target="_blank"
            rel="noopener noreferrer"
            className="link-url"
          >
            {linkUrl}
          </a>
          <button onClick={handleLinkEdit} className="link-btn">
            <Edit size={16} />
          </button>
          <button onClick={handleLinkRemove} className="link-btn">
            <Trash2 size={16} />
          </button>
        </div>
      )}
    </div>
  );
}