import { CodeHighlightNode, CodeNode } from "@lexical/code";
import { AutoLinkNode, LinkNode } from "@lexical/link";
import { ListItemNode, ListNode } from "@lexical/list";
import { TRANSFORMERS } from "@lexical/markdown";
import { LexicalComposer } from "@lexical/react/LexicalComposer";
import { HorizontalRuleNode } from "@lexical/react/LexicalHorizontalRuleNode";
import { MarkdownShortcutPlugin } from "@lexical/react/LexicalMarkdownShortcutPlugin";
import { HeadingNode, QuoteNode } from "@lexical/rich-text";
import { TableCellNode, TableNode, TableRowNode } from "@lexical/table";
import { ParagraphNode, TextNode } from "lexical";

import { Editor } from "./Editor";
import exampleTheme from "./EditorTheme";

function onError(error) {
  console.error(error);
}

export function EditorComposer() {
  const initialConfig = {
    namespace: "HTML5Editor",
    theme: exampleTheme,
    onError,
    nodes: [
      ParagraphNode,
      TextNode,
      HeadingNode,
      ListNode,
      ListItemNode,
      QuoteNode,
      CodeNode,
      CodeHighlightNode,
      TableNode,
      TableCellNode,
      TableRowNode,
      AutoLinkNode,
      LinkNode,
      HorizontalRuleNode,
    ],
    html: {
      export: {
        // Configure all nodes to export clean HTML without classes
        paragraph: (node, element) => {
          const p = document.createElement('p');
          return { element: p };
        },
        text: (node, element) => {
          // Return the element as-is for text nodes
          return { element };
        },
        heading: (node, element) => {
          const tag = element.tagName.toLowerCase();
          const heading = document.createElement(tag);
          return { element: heading };
        },
        list: (node, element) => {
          const tag = element.tagName.toLowerCase();
          const list = document.createElement(tag);
          return { element: list };
        },
        listitem: (node, element) => {
          const li = document.createElement('li');
          return { element: li };
        },
        quote: (node, element) => {
          const blockquote = document.createElement('blockquote');
          return { element: blockquote };
        },
        code: (node, element) => {
          const pre = document.createElement('pre');
          const code = document.createElement('code');
          pre.appendChild(code);
          return { element: pre };
        },
        table: (node, element) => {
          const table = document.createElement('table');
          return { element: table };
        },
        tablerow: (node, element) => {
          const tr = document.createElement('tr');
          return { element: tr };
        },
        tablecell: (node, element) => {
          const td = document.createElement('td');
          return { element: td };
        },
        link: (node, element) => {
          const a = document.createElement('a');
          if (element.href) {
            a.href = element.href;
          }
          return { element: a };
        }
      }
    }
  };

  return (
    <LexicalComposer initialConfig={initialConfig}>
      <div className="editor-composer">
        <Editor />
        <MarkdownShortcutPlugin transformers={TRANSFORMERS} />
      </div>
    </LexicalComposer>
  );
}
