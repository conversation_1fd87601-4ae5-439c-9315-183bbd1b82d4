import { $getRoot, $getSelection } from "lexical";
import { $generateHtmlFromNodes, $generateNodesFromDOM } from "@lexical/html";
import { useLexicalComposerContext } from "@lexical/react/LexicalComposerContext";
import { RichTextPlugin } from "@lexical/react/LexicalRichTextPlugin";
import { ContentEditable } from "@lexical/react/LexicalContentEditable";
import { HistoryPlugin } from "@lexical/react/LexicalHistoryPlugin";
import { LexicalErrorBoundary } from "@lexical/react/LexicalErrorBoundary";
import { ListPlugin } from "@lexical/react/LexicalListPlugin";
import { LinkPlugin } from "@lexical/react/LexicalLinkPlugin";
import { TablePlugin } from "@lexical/react/LexicalTablePlugin";
import { AutoFocusPlugin } from "@lexical/react/LexicalAutoFocusPlugin";
import { useState, useEffect } from "react";

import { Toolbar } from "./Toolbar";
import { FloatingLinkEditor } from "./FloatingLinkEditor";
import { FloatingTextFormatToolbar } from "./FloatingTextFormatToolbar";

export function Editor() {
  const [editor] = useLexicalComposerContext();
  const [htmlContent, setHtmlContent] = useState("");
  const [isLinkEditMode, setIsLinkEditMode] = useState(false);
  const [wordCount, setWordCount] = useState(0);

  // Update HTML preview in real-time
  useEffect(() => {
    const updateHtml = () => {
      editor.update(() => {
        const htmlString = $generateHtmlFromNodes(editor, null);
        // Ensure empty content shows as empty paragraph
        const processedHtml = htmlString || "<p><br></p>";
        setHtmlContent(htmlString);
      });
    };

    const removeUpdateListener = editor.registerUpdateListener(() => {
      updateHtml();
      // Also update word count
      editor.getEditorState().read(() => {
        const root = $getRoot();
        const text = root.getTextContent();
        const words = text
          .trim()
          .split(/\s+/)
          .filter((word) => word.length > 0);
        setWordCount(text.trim() === "" ? 0 : words.length);
      });
    });

    // Initial update
    updateHtml();

    return removeUpdateListener;
  }, [editor]);

  const saveAsHtml = () => {
    const fullHtmlDocument = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <style>
        body {
            font-family: Georgia, 'Times New Roman', serif;
            line-height: 1.6;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            color: #333;
        }
        h1, h2, h3, h4, h5, h6 {
            color: #2c3e50;
            margin-top: 1.5em;
            margin-bottom: 0.5em;
        }
        blockquote {
            border-left: 4px solid #3498db;
            padding-left: 20px;
            margin: 20px 0;
            font-style: italic;
            background: #f8f9fa;
            padding: 15px 20px;
        }
        code {
            background: #f1f3f4;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: 'Monaco', 'Menlo', monospace;
        }
        pre {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 20px;
            border-radius: 8px;
            overflow-x: auto;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 20px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        th {
            background: #3498db;
            color: white;
        }
        a {
            color: #3498db;
            text-decoration: none;
        }
        a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    ${htmlContent}
</body>
</html>`;

    const blob = new Blob([fullHtmlDocument], { type: "text/html" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = "document.html";
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <div className="editor-container">
      <Toolbar editor={editor} wordCount={wordCount} onSave={saveAsHtml} />

      <div className="editor-split-view">
        {/* Editor Panel */}
        <div className="editor-panel">
          <div className="panel-header">
            <h3>Editor</h3>
          </div>
          <div className="editor-content">
            <RichTextPlugin
              contentEditable={
                <ContentEditable
                  className="editor-input"
                  ariaLabel="Rich text editor"
                  placeholder="Start typing your content here..."
                />
              }
              placeholder={
                <div className="editor-placeholder">
                  Start typing your content here...
                </div>
              }
              ErrorBoundary={LexicalErrorBoundary}
            />
            <HistoryPlugin />
            <ListPlugin />
            <LinkPlugin />
            <TablePlugin />
            <AutoFocusPlugin />
            <FloatingTextFormatToolbar />
            <FloatingLinkEditor
              isLinkEditMode={isLinkEditMode}
              setIsLinkEditMode={setIsLinkEditMode}
            />
          </div>
        </div>

        {/* Preview Panel */}
        <div className="preview-panel">
          <div className="panel-header">
            <h3>HTML Preview</h3>
          </div>
          <div className="preview-content">
            <pre className="html-preview">{htmlContent}</pre>
          </div>
        </div>
      </div>

      <div className="editor-footer">
        <div className="footer-stats">
          <span className="word-count">Words: {wordCount}</span>
          <span className="char-count">Characters: {htmlContent.length}</span>
        </div>
        <button onClick={saveAsHtml} className="save-btn">
          Save as HTML
        </button>
      </div>
    </div>
  );
}
