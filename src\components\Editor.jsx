import { $generateHtmlFromNodes } from "@lexical/html";
import { AutoFocusPlugin } from "@lexical/react/LexicalAutoFocusPlugin";
import { useLexicalComposerContext } from "@lexical/react/LexicalComposerContext";
import { ContentEditable } from "@lexical/react/LexicalContentEditable";
import { LexicalErrorBoundary } from "@lexical/react/LexicalErrorBoundary";
import { HistoryPlugin } from "@lexical/react/LexicalHistoryPlugin";
import { LinkPlugin } from "@lexical/react/LexicalLinkPlugin";
import { ListPlugin } from "@lexical/react/LexicalListPlugin";
import { RichTextPlugin } from "@lexical/react/LexicalRichTextPlugin";
import { TablePlugin } from "@lexical/react/LexicalTablePlugin";
import { $getRoot } from "lexical";
import { useEffect, useState } from "react";

import { FloatingLinkEditor } from "./FloatingLinkEditor";
import { FloatingTextFormatToolbar } from "./FloatingTextFormatToolbar";
import { Toolbar } from "./Toolbar";

export function Editor() {
  const [editor] = useLexicalComposerContext();
  const [htmlContent, setHtmlContent] = useState("");
  const [isLinkEditMode, setIsLinkEditMode] = useState(false);
  const [wordCount, setWordCount] = useState(0);

  // Function to clean HTML by removing Lexical-specific classes and attributes
  const cleanHtml = (html) => {
    if (!html || html.trim() === '') return "<p></p>";

    // Create a temporary DOM element to parse and clean the HTML
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = html;

    // Remove Lexical-specific attributes but keep essential HTML structure
    const allElements = tempDiv.querySelectorAll('*');
    allElements.forEach(element => {
      // Remove Lexical-specific classes and attributes
      if (element.className && element.className.includes('editor-')) {
        element.removeAttribute('class');
      }
      element.removeAttribute('dir');
      element.removeAttribute('data-lexical-editor');
      element.removeAttribute('data-lexical-text');
      element.removeAttribute('spellcheck');
      element.removeAttribute('contenteditable');
      element.removeAttribute('role');
      element.removeAttribute('tabindex');
      element.removeAttribute('data-lexical-decorator');
      element.removeAttribute('data-lexical-hash');

      // Remove empty class attributes
      if (element.className === '') {
        element.removeAttribute('class');
      }

      // Clean up empty paragraphs but keep structure
      if (element.tagName === 'P' && (element.innerHTML === '<br>' || element.innerHTML === '<br/>')) {
        element.innerHTML = '';
      }
    });

    const result = tempDiv.innerHTML;
    console.log('Cleaned HTML:', result); // Debug log
    return result || "<p></p>";
  };

  // Update HTML preview in real-time
  useEffect(() => {
    const updateHtml = () => {
      // Try to get HTML from the editor's DOM element
      const editorElement = editor.getRootElement();
      if (editorElement) {
        const rawHtml = editorElement.innerHTML;
        console.log('Raw HTML from DOM:', rawHtml); // Debug log
        const cleanedHtml = cleanHtml(rawHtml);
        setHtmlContent(cleanedHtml);
      } else {
        // Fallback to Lexical's HTML generation
        editor.getEditorState().read(() => {
          const htmlString = $generateHtmlFromNodes(editor, null);
          console.log('HTML from Lexical:', htmlString); // Debug log
          const cleanedHtml = cleanHtml(htmlString);
          setHtmlContent(cleanedHtml);
        });
      }
    };

    const removeUpdateListener = editor.registerUpdateListener(() => {
      updateHtml();
      // Also update word count
      editor.getEditorState().read(() => {
        const root = $getRoot();
        const text = root.getTextContent();
        const words = text
          .trim()
          .split(/\s+/)
          .filter((word) => word.length > 0);
        setWordCount(text.trim() === "" ? 0 : words.length);
      });
    });

    // Initial update
    updateHtml();

    return removeUpdateListener;
  }, [editor]);

  const saveAsHtml = () => {
    // Use the exact HTML content that's displayed in the preview panel
    // This ensures what you see is what you get in the download
    const previewElement = document.querySelector('.html-preview');
    let currentHtmlContent = htmlContent;

    if (previewElement) {
      // Get the text content from the preview panel (which shows the raw HTML)
      const previewText = previewElement.textContent;
      console.log('Preview HTML:', previewText); // Debug log

      // If the preview has content, use it directly
      if (previewText && previewText.trim() !== '') {
        currentHtmlContent = previewText;
      }
    }

    // Fallback to stored content
    if (!currentHtmlContent || currentHtmlContent.trim() === '') {
      currentHtmlContent = "<p></p>";
    }

    const fullHtmlDocument = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <style>
        body {
            font-family: Georgia, 'Times New Roman', serif;
            line-height: 1.6;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            color: #333;
        }
        h1, h2, h3, h4, h5, h6 {
            color: #2c3e50;
            margin-top: 1.5em;
            margin-bottom: 0.5em;
        }
        blockquote {
            border-left: 4px solid #3498db;
            padding-left: 20px;
            margin: 20px 0;
            font-style: italic;
            background: #f8f9fa;
            padding: 15px 20px;
        }
        code {
            background: #f1f3f4;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: 'Monaco', 'Menlo', monospace;
        }
        pre {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 20px;
            border-radius: 8px;
            overflow-x: auto;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 20px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        th {
            background: transparent;
            color: inherit;
            font-weight: bold;
        }
        a {
            color: #3498db;
            text-decoration: none;
        }
        a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    ${currentHtmlContent}
</body>
</html>`;

    const blob = new Blob([fullHtmlDocument], { type: "text/html" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = "document.html";
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <div className="editor-container">
      <Toolbar editor={editor} wordCount={wordCount} onSave={saveAsHtml} />

      <div className="editor-split-view">
        {/* Editor Panel */}
        <div className="editor-panel">
          <div className="panel-header">
            <h3>Editor</h3>
          </div>
          <div className="editor-content">
            <RichTextPlugin
              contentEditable={
                <ContentEditable
                  className="editor-input"
                  ariaLabel="Rich text editor"
                  placeholder="Start typing your content here..."
                />
              }
              placeholder={
                <div className="editor-placeholder">
                  Start typing your content here...
                </div>
              }
              ErrorBoundary={LexicalErrorBoundary}
            />
            <HistoryPlugin />
            <ListPlugin />
            <LinkPlugin />
            <TablePlugin />
            <AutoFocusPlugin />
            <FloatingTextFormatToolbar />
            <FloatingLinkEditor
              isLinkEditMode={isLinkEditMode}
              setIsLinkEditMode={setIsLinkEditMode}
            />
          </div>
        </div>

        {/* Preview Panel */}
        <div className="preview-panel">
          <div className="panel-header">
            <h3>HTML Preview</h3>
          </div>
          <div className="preview-content">
            <pre className="html-preview">{htmlContent}</pre>
          </div>
        </div>
      </div>

      <div className="editor-footer">
        <div className="footer-stats">
          <span className="word-count">Words: {wordCount}</span>
          <span className="char-count">Characters: {htmlContent.length}</span>
        </div>
        <button onClick={saveAsHtml} className="save-btn">
          Save as HTML
        </button>
      </div>
    </div>
  );
}
