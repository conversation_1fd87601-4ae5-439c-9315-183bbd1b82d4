import { $createCodeNode } from "@lexical/code";
import { TOGGLE_LINK_COMMAND } from "@lexical/link";
import {
  INSERT_ORDERED_LIST_COMMAND,
  INSERT_UNORDERED_LIST_COMMAND,
} from "@lexical/list";
import { $createHorizontalRuleNode } from "@lexical/react/LexicalHorizontalRuleNode";
import { $createHeadingNode, $createQuoteNode } from "@lexical/rich-text";
import { $setBlocksType } from "@lexical/selection";
import {
  $createParagraphNode,
  $getSelection,
  $isRangeSelection,
  FORMAT_ELEMENT_COMMAND,
  FORMAT_TEXT_COMMAND,
  INDENT_CONTENT_COMMAND, OUTDENT_CONTENT_COMMAND,
  REDO_COMMAND,
  UNDO_COMMAND,
} from "lexical";
import {
  AlignCenter,
  AlignJustify,
  AlignLeft,
  AlignRight,
  Bold,
  Clipboard,
  Code,
  Copy,
  Image,
  Indent,
  Italic,
  Link,
  List,
  ListOrdered,
  Minus,
  Outdent,
  Palette,
  Quote,
  Redo,
  Save,
  Scissors,
  Strikethrough,
  Subscript,
  Superscript,
  Table,
  Type,
  Underline,
  Undo,
} from "lucide-react";
import React, { useCallback, useEffect, useRef, useState } from "react";

export function Toolbar({ editor, wordCount, onSave }) {
  const [isBold, setIsBold] = useState(false);
  const [isItalic, setIsItalic] = useState(false);
  const [isUnderline, setIsUnderline] = useState(false);
  const [isStrikethrough, setIsStrikethrough] = useState(false);
  const [isSubscript, setIsSubscript] = useState(false);
  const [isSuperscript, setIsSuperscript] = useState(false);
  const [blockType, setBlockType] = useState("paragraph");
  const [fontSize, setFontSize] = useState("16");
  const [fontFamily, setFontFamily] = useState("Georgia");
  const [textColor, setTextColor] = useState("#000000");
  const [backgroundColor, setBackgroundColor] = useState("#ffffff");
  const [isTableGridOpen, setIsTableGridOpen] = useState(false);
  const tableButtonRef = useRef(null);

  const updateToolbar = useCallback(() => {
    const selection = $getSelection();
    if ($isRangeSelection(selection)) {
      setIsBold(selection.hasFormat("bold"));
      setIsItalic(selection.hasFormat("italic"));
      setIsUnderline(selection.hasFormat("underline"));
      setIsStrikethrough(selection.hasFormat("strikethrough"));
      setIsSubscript(selection.hasFormat("subscript"));
      setIsSuperscript(selection.hasFormat("superscript"));
    }
  }, []);

  useEffect(() => {
    return editor.registerUpdateListener(({ editorState }) => {
      editorState.read(() => {
        updateToolbar();
      });
    });
  }, [editor, updateToolbar]);

  const formatText = (format) => {
    editor.dispatchCommand(FORMAT_TEXT_COMMAND, format);
  };

  const formatElement = (format) => {
    editor.dispatchCommand(FORMAT_ELEMENT_COMMAND, format);
  };

  const insertHeading = (headingSize) => {
    editor.update(() => {
      const selection = $getSelection();
      if ($isRangeSelection(selection)) {
        $setBlocksType(selection, () => $createHeadingNode(headingSize));
      }
    });
  };

  const insertQuote = () => {
    editor.update(() => {
      const selection = $getSelection();
      if ($isRangeSelection(selection)) {
        $setBlocksType(selection, () => $createQuoteNode());
      }
    });
  };

  const insertCode = () => {
    editor.update(() => {
      const selection = $getSelection();
      if ($isRangeSelection(selection)) {
        $setBlocksType(selection, () => $createCodeNode());
      }
    });
  };

  const insertHorizontalRule = () => {
    editor.update(() => {
      const selection = $getSelection();
      if ($isRangeSelection(selection)) {
        const focusNode = selection.focus.getNode();
        focusNode
          .getTopLevelElementOrThrow()
          .insertAfter($createHorizontalRuleNode());
      }
    });
  };

  const insertList = (type) => {
    if (type === "ul") {
      editor.dispatchCommand(INSERT_UNORDERED_LIST_COMMAND, undefined);
    } else {
      editor.dispatchCommand(INSERT_ORDERED_LIST_COMMAND, undefined);
    }
  };

  const insertLink = () => {
    editor.dispatchCommand(TOGGLE_LINK_COMMAND, "https://");
  };

  const toggleTableGrid = () => {
    setIsTableGridOpen(!isTableGridOpen);
  };

  const insertImage = () => {
    const url = prompt("Enter image URL:");
    if (url) {
      editor.update(() => {
        const selection = $getSelection();
        if ($isRangeSelection(selection)) {
          const imageNode = document.createElement("img");
          imageNode.src = url;
          imageNode.style.maxWidth = "100%";
          imageNode.style.height = "auto";
          selection.insertRawText(
            `<img src="${url}" style="max-width: 100%; height: auto;" />`
          );
        }
      });
    }
  };

  const copyContent = () => {
    editor.getEditorState().read(() => {
      const selection = $getSelection();
      if ($isRangeSelection(selection)) {
        const text = selection.getTextContent();
        navigator.clipboard.writeText(text);
      }
    });
  };

  const cutContent = () => {
    editor.getEditorState().read(() => {
      const selection = $getSelection();
      if ($isRangeSelection(selection)) {
        const text = selection.getTextContent();
        navigator.clipboard.writeText(text);
        selection.removeText();
      }
    });
  };

  const pasteContent = async () => {
    try {
      const text = await navigator.clipboard.readText();
      editor.update(() => {
        const selection = $getSelection();
        if ($isRangeSelection(selection)) {
          selection.insertText(text);
        }
      });
    } catch (err) {
      console.error("Failed to paste:", err);
    }
  };

  const indentContent = () => {
    editor.dispatchCommand(INDENT_CONTENT_COMMAND, undefined);
  };

  const outdentContent = () => {
    editor.dispatchCommand(OUTDENT_CONTENT_COMMAND, undefined);
  };

  const resetToParagraph = () => {
    editor.update(() => {
      const selection = $getSelection();
      if ($isRangeSelection(selection)) {
        $setBlocksType(selection, () => $createParagraphNode());
      }
    });
    setBlockType("paragraph");
  };

  return (
    <div className="toolbar">
      {/* File Operations */}
      <div className="toolbar-group">
        <button
          onClick={onSave}
          className="toolbar-btn save-btn-toolbar"
          title="Save as HTML"
        >
          <Save size={16} />
          <span>Save HTML</span>
        </button>
      </div>

      {/* Undo/Redo */}
      <div className="toolbar-group">
        <button
          onClick={() => editor.dispatchCommand(UNDO_COMMAND, undefined)}
          className="toolbar-btn"
          title="Undo"
        >
          <Undo size={16} />
        </button>
        <button
          onClick={() => editor.dispatchCommand(REDO_COMMAND, undefined)}
          className="toolbar-btn"
          title="Redo"
        >
          <Redo size={16} />
        </button>
      </div>

      {/* Block Type */}
      <div className="toolbar-group">
        <select
          onChange={(e) => {
            const value = e.target.value;
            if (value === "paragraph") {
              resetToParagraph();
            } else if (value.startsWith("h")) {
              insertHeading(value);
            }
          }}
          className="toolbar-select"
          value={blockType}
        >
          <option value="paragraph">Paragraph</option>
          <option value="h1">Heading 1</option>
          <option value="h2">Heading 2</option>
          <option value="h3">Heading 3</option>
          <option value="h4">Heading 4</option>
          <option value="h5">Heading 5</option>
          <option value="h6">Heading 6</option>
        </select>
      </div>

      {/* Font Controls */}
      <div className="toolbar-group">
        <select
          onChange={(e) => setFontFamily(e.target.value)}
          className="toolbar-select"
          value={fontFamily}
          title="Font Family"
        >
          <option value="Georgia">Georgia</option>
          <option value="Arial">Arial</option>
          <option value="Times New Roman">Times New Roman</option>
          <option value="Helvetica">Helvetica</option>
          <option value="Verdana">Verdana</option>
          <option value="Courier New">Courier New</option>
        </select>
        <select
          onChange={(e) => setFontSize(e.target.value)}
          className="toolbar-select"
          value={fontSize}
          title="Font Size"
        >
          <option value="12">12px</option>
          <option value="14">14px</option>
          <option value="16">16px</option>
          <option value="18">18px</option>
          <option value="20">20px</option>
          <option value="24">24px</option>
          <option value="28">28px</option>
          <option value="32">32px</option>
        </select>
      </div>

      {/* Color Controls */}
      <div className="toolbar-group">
        <div className="color-picker-wrapper">
          <input
            type="color"
            value={textColor}
            onChange={(e) => setTextColor(e.target.value)}
            className="color-picker"
            title="Text Color"
          />
          <Type size={16} />
        </div>
        <div className="color-picker-wrapper">
          <input
            type="color"
            value={backgroundColor}
            onChange={(e) => setBackgroundColor(e.target.value)}
            className="color-picker"
            title="Background Color"
          />
          <Palette size={16} />
        </div>
      </div>

      {/* Text Formatting */}
      <div className="toolbar-group">
        <button
          onClick={() => formatText("bold")}
          className={`toolbar-btn ${isBold ? "active" : ""}`}
          title="Bold"
        >
          <Bold size={16} />
        </button>
        <button
          onClick={() => formatText("italic")}
          className={`toolbar-btn ${isItalic ? "active" : ""}`}
          title="Italic"
        >
          <Italic size={16} />
        </button>
        <button
          onClick={() => formatText("underline")}
          className={`toolbar-btn ${isUnderline ? "active" : ""}`}
          title="Underline"
        >
          <Underline size={16} />
        </button>
        <button
          onClick={() => formatText("strikethrough")}
          className={`toolbar-btn ${isStrikethrough ? "active" : ""}`}
          title="Strikethrough"
        >
          <Strikethrough size={16} />
        </button>
        <button
          onClick={() => formatText("subscript")}
          className={`toolbar-btn ${isSubscript ? "active" : ""}`}
          title="Subscript"
        >
          <Subscript size={16} />
        </button>
        <button
          onClick={() => formatText("superscript")}
          className={`toolbar-btn ${isSuperscript ? "active" : ""}`}
          title="Superscript"
        >
          <Superscript size={16} />
        </button>
      </div>

      {/* Alignment */}
      <div className="toolbar-group">
        <button
          onClick={() => formatElement("left")}
          className="toolbar-btn"
          title="Align Left"
        >
          <AlignLeft size={16} />
        </button>
        <button
          onClick={() => formatElement("center")}
          className="toolbar-btn"
          title="Align Center"
        >
          <AlignCenter size={16} />
        </button>
        <button
          onClick={() => formatElement("right")}
          className="toolbar-btn"
          title="Align Right"
        >
          <AlignRight size={16} />
        </button>
        <button
          onClick={() => formatElement("justify")}
          className="toolbar-btn"
          title="Justify"
        >
          <AlignJustify size={16} />
        </button>
      </div>

      {/* Lists */}
      <div className="toolbar-group">
        <button
          onClick={() => insertList("ul")}
          className="toolbar-btn"
          title="Bullet List"
        >
          <List size={16} />
        </button>
        <button
          onClick={() => insertList("ol")}
          className="toolbar-btn"
          title="Numbered List"
        >
          <ListOrdered size={16} />
        </button>
      </div>

      {/* Indentation */}
      <div className="toolbar-group">
        <button
          onClick={indentContent}
          className="toolbar-btn"
          title="Increase Indent"
        >
          <Indent size={16} />
        </button>
        <button
          onClick={outdentContent}
          className="toolbar-btn"
          title="Decrease Indent"
        >
          <Outdent size={16} />
        </button>
      </div>

      {/* Block Elements */}
      <div className="toolbar-group">
        <button onClick={insertQuote} className="toolbar-btn" title="Quote">
          <Quote size={16} />
        </button>
        <button onClick={insertCode} className="toolbar-btn" title="Code Block">
          <Code size={16} />
        </button>
        <button
          onClick={insertHorizontalRule}
          className="toolbar-btn"
          title="Horizontal Rule"
        >
          <Minus size={16} />
        </button>
      </div>

      {/* Insert Elements */}
      <div className="toolbar-group">
        <button
          onClick={insertLink}
          className="toolbar-btn"
          title="Insert Link"
        >
          <Link size={16} />
        </button>
        <div className="table-button-container">
          <button
            ref={tableButtonRef}
            onClick={toggleTableGrid}
            className="toolbar-btn"
            title="Insert Table"
          >
            <Table size={16} />
          </button>
          <TableGridSelector
            isOpen={isTableGridOpen}
            onClose={() => setIsTableGridOpen(false)}
            triggerRef={tableButtonRef}
          />
        </div>
        <button
          onClick={insertImage}
          className="toolbar-btn"
          title="Insert Image"
        >
          <Image size={16} />
        </button>
      </div>

      {/* Clipboard Operations */}
      <div className="toolbar-group">
        <button onClick={copyContent} className="toolbar-btn" title="Copy">
          <Copy size={16} />
        </button>
        <button onClick={cutContent} className="toolbar-btn" title="Cut">
          <Scissors size={16} />
        </button>
        <button onClick={pasteContent} className="toolbar-btn" title="Paste">
          <Clipboard size={16} />
        </button>
      </div>

      {/* Content Management */}
      <div className="toolbar-group">
        <button
          onClick={clearContent}
          className="toolbar-btn clear-btn"
          title="Clear All Content"
        >
          <Trash2 size={16} />
          <span>Clear</span>
        </button>
      </div>
    </div>
  );
}
