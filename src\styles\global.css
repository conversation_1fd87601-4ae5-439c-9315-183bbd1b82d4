* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Robot<PERSON>", "Oxygen",
    "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue",
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f8f9fa;
  color: #333;
  line-height: 1.6;
}

/* App Layout */
.app {
  min-height: 100vh;
  padding: 1rem;
}

.container {
  max-width: 1400px;
  margin: 0 auto;
}

.header {
  text-align: center;
  margin-bottom: 2rem;
  padding: 2rem 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.header h1 {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.header p {
  font-size: 1.1rem;
  opacity: 0.9;
}

.footer {
  text-align: center;
  margin-top: 2rem;
  padding: 1rem;
  color: #6c757d;
  font-size: 0.9rem;
}

/* Editor Container */
.editor-composer {
  width: 100%;
}

.editor-container {
  width: 100%;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  border: 1px solid #e9ecef;
}

/* Toolbar */
.toolbar {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 8px;
  padding: 16px 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 2px solid #dee2e6;
}

.toolbar-group {
  display: flex;
  align-items: center;
  gap: 4px;
  border-right: 1px solid #ced4da;
  padding-right: 12px;
  margin-right: 12px;
}

.toolbar-group:last-child {
  border-right: none;
  padding-right: 0;
  margin-right: 0;
}

.toolbar-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  min-width: 36px;
  height: 36px;
  padding: 0 8px;
  border: none;
  background: transparent;
  border-radius: 6px;
  cursor: pointer;
  color: #495057;
  transition: all 0.2s ease;
  font-size: 14px;
  font-weight: 500;
}

.toolbar-btn:hover {
  background: #ffffff;
  color: #212529;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.toolbar-btn.active {
  background: #007bff;
  color: white;
  box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
}

.save-btn-toolbar {
  background: #28a745;
  color: white;
  font-weight: 600;
}

.save-btn-toolbar:hover {
  background: #218838;
  transform: translateY(-1px);
}

.toolbar-select {
  padding: 8px 12px;
  border: 2px solid #ced4da;
  border-radius: 6px;
  background: white;
  color: #495057;
  font-size: 14px;
  min-width: 140px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.toolbar-select:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

/* Color Picker Controls */
.color-picker-wrapper {
  display: flex;
  align-items: center;
  gap: 4px;
  position: relative;
}

.color-picker {
  width: 32px;
  height: 32px;
  border: 2px solid #ced4da;
  border-radius: 6px;
  cursor: pointer;
  background: none;
  padding: 0;
  transition: all 0.2s ease;
}

.color-picker:hover {
  border-color: #007bff;
  transform: scale(1.05);
}

.color-picker::-webkit-color-swatch-wrapper {
  padding: 0;
  border: none;
  border-radius: 4px;
}

.color-picker::-webkit-color-swatch {
  border: none;
  border-radius: 4px;
}

.clear-btn {
  background: #dc3545;
  color: white;
}

.clear-btn:hover {
  background: #c82333;
  transform: translateY(-1px);
}

/* Split View Layout */
.editor-split-view {
  display: grid;
  grid-template-columns: 1fr 1fr;
  min-height: 600px;
}

.editor-panel,
.preview-panel {
  display: flex;
  flex-direction: column;
}

.editor-panel {
  border-right: 2px solid #e9ecef;
}

.panel-header {
  padding: 16px 20px;
  background: linear-gradient(135deg, #f1f3f4 0%, #e8eaed 100%);
  border-bottom: 1px solid #e9ecef;
}

.panel-header h3 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #495057;
  margin: 0;
}

.editor-content,
.preview-content {
  flex: 1;
  overflow: auto;
}

/* Editor Input */
.editor-input {
  min-height: 100%;
  padding: 24px;
  border: none;
  outline: none;
  font-size: 16px;
  line-height: 1.7;
  color: #2c3e50;
  caret-color: #007bff;
  font-family: Georgia, "Times New Roman", serif;
  background: white;
}

/* Editor Text Formatting */
.editor-text-bold {
  font-weight: bold;
}

.editor-text-italic {
  font-style: italic;
}

.editor-text-underline {
  text-decoration: underline;
}

.editor-text-strikethrough {
  text-decoration: line-through;
}

.editor-text-underlineStrikethrough {
  text-decoration: underline line-through;
}

.editor-text-subscript {
  font-size: 0.8em;
  vertical-align: sub;
}

.editor-text-superscript {
  font-size: 0.8em;
  vertical-align: super;
}

.editor-text-code {
  background: #f1f3f4;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: "Monaco", "Menlo", monospace;
  font-size: 0.9em;
}

.editor-paragraph {
  margin: 0 0 1em 0;
}

.editor-quote {
  margin: 1.5em 0;
  padding: 20px 24px;
  border-left: 5px solid #3498db;
  background: #f8f9fa;
  font-style: italic;
}

.editor-heading-h1,
.editor-heading-h2,
.editor-heading-h3,
.editor-heading-h4,
.editor-heading-h5,
.editor-heading-h6 {
  font-weight: 600;
  margin: 1.5em 0 0.5em 0;
  color: #2c3e50;
}

.editor-heading-h1 {
  font-size: 2.5em;
}
.editor-heading-h2 {
  font-size: 2em;
}
.editor-heading-h3 {
  font-size: 1.75em;
}
.editor-heading-h4 {
  font-size: 1.5em;
}
.editor-heading-h5 {
  font-size: 1.25em;
}
.editor-heading-h6 {
  font-size: 1em;
  text-transform: uppercase;
}

.editor-list-ol,
.editor-list-ul {
  margin: 1em 0;
  padding-left: 2em;
}

.editor-listitem {
  margin-bottom: 0.5em;
}

.editor-link {
  color: #3498db;
  text-decoration: none;
  border-bottom: 1px solid transparent;
}

.editor-link:hover {
  border-bottom-color: #3498db;
}

.editor-code {
  background: #2c3e50;
  color: #ecf0f1;
  padding: 20px;
  border-radius: 8px;
  font-family: "Monaco", "Menlo", monospace;
  margin: 1.5em 0;
}

.editor-table {
  border-collapse: collapse;
  width: 100%;
  margin: 1.5em 0;
}

.editor-tableCell,
.editor-tableCellHeader {
  border: 1px solid #bdc3c7;
  padding: 12px;
  text-align: left;
}

.editor-tableCellHeader {
  background: #3498db;
  color: white;
  font-weight: 600;
}

.editor-input:focus {
  outline: none;
}

/* Placeholder */
.editor-placeholder {
  position: absolute;
  top: 24px;
  left: 24px;
  color: #95a5a6;
  font-size: 16px;
  pointer-events: none;
  user-select: none;
  font-style: italic;
}

/* HTML Preview */
.html-preview {
  padding: 24px;
  font-family: "Monaco", "Menlo", "Courier New", monospace;
  font-size: 14px;
  line-height: 1.5;
  color: #2c3e50;
  background: #fafbfc;
  min-height: 100%;
  white-space: pre-wrap;
  word-break: break-all;
  tab-size: 2;
}

.html-preview:empty::before {
  content: "Your formatted content will appear here...";
  color: #95a5a6;
  font-style: italic;
}

.html-preview h1,
.html-preview h2,
.html-preview h3,
.html-preview h4,
.html-preview h5,
.html-preview h6 {
  color: #2c3e50;
  margin-top: 1.5em;
  margin-bottom: 0.5em;
  font-weight: 600;
}

.html-preview h1 {
  font-size: 2.5rem;
  border-bottom: 3px solid #3498db;
  padding-bottom: 12px;
}

.html-preview h2 {
  font-size: 2rem;
  border-bottom: 2px solid #95a5a6;
  padding-bottom: 8px;
}

.html-preview h3 {
  font-size: 1.75rem;
}

.html-preview h4 {
  font-size: 1.5rem;
}

.html-preview h5 {
  font-size: 1.25rem;
}

.html-preview h6 {
  font-size: 1rem;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.html-preview p {
  margin-bottom: 1.2em;
}

.html-preview blockquote {
  margin: 1.5em 0;
  padding: 20px 24px;
  border-left: 5px solid #3498db;
  background: linear-gradient(135deg, #ecf0f1 0%, #bdc3c7 100%);
  font-style: italic;
  border-radius: 0 8px 8px 0;
  position: relative;
}

.html-preview blockquote::before {
  content: '"';
  font-size: 4rem;
  color: #3498db;
  position: absolute;
  top: -10px;
  left: 10px;
  font-family: Georgia, serif;
}

.html-preview code {
  background: #f1f3f4;
  padding: 3px 6px;
  border-radius: 4px;
  font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace;
  font-size: 0.9em;
  color: #e74c3c;
}

.html-preview pre {
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  color: #ecf0f1;
  padding: 20px;
  border-radius: 8px;
  overflow-x: auto;
  margin: 1.5em 0;
  font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace;
  font-size: 14px;
  line-height: 1.6;
}

.html-preview ul,
.html-preview ol {
  margin: 1em 0;
  padding-left: 2em;
}

.html-preview li {
  margin-bottom: 0.5em;
}

.html-preview a {
  color: #3498db;
  text-decoration: none;
  font-weight: 500;
  border-bottom: 1px solid transparent;
  transition: all 0.3s ease;
}

.html-preview a:hover {
  color: #2980b9;
  border-bottom-color: #2980b9;
  background: rgba(52, 152, 219, 0.1);
  padding: 2px 4px;
  border-radius: 3px;
}

.html-preview sub {
  font-size: 0.8em;
  vertical-align: sub;
}

.html-preview sup {
  font-size: 0.8em;
  vertical-align: super;
}

.html-preview strong {
  font-weight: bold;
}

.html-preview em {
  font-style: italic;
}

.html-preview u {
  text-decoration: underline;
}

.html-preview s,
.html-preview del {
  text-decoration: line-through;
}

.html-preview img {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
  margin: 1em 0;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.html-preview table {
  border-collapse: collapse;
  width: 100%;
  margin: 1.5em 0;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.html-preview th,
.html-preview td {
  border: 1px solid #bdc3c7;
  padding: 12px 16px;
  text-align: left;
}

.html-preview th {
  background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
  color: white;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.html-preview td {
  background: white;
}

.html-preview hr {
  border: none;
  border-top: 3px solid #bdc3c7;
  margin: 2em 0;
  background: linear-gradient(
    90deg,
    transparent 0%,
    #bdc3c7 50%,
    transparent 100%
  );
  height: 3px;
}

/* Editor Footer */
.editor-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-top: 2px solid #dee2e6;
}

.footer-stats {
  display: flex;
  gap: 20px;
  font-size: 14px;
  color: #6c757d;
}

.word-count,
.char-count {
  font-weight: 600;
  color: #495057;
}

.save-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

.save-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
}

/* Text Formatting Classes */
.editor-text-bold {
  font-weight: bold;
}

.editor-text-italic {
  font-style: italic;
}

.editor-text-underline {
  text-decoration: underline;
}

.editor-text-strikethrough {
  text-decoration: line-through;
}

.editor-text-underlineStrikethrough {
  text-decoration: underline line-through;
}

.editor-text-subscript {
  vertical-align: sub;
  font-size: 0.8em;
}

.editor-text-superscript {
  vertical-align: super;
  font-size: 0.8em;
}

.editor-text-code {
  background: #f1f3f4;
  padding: 3px 6px;
  border-radius: 4px;
  font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace;
  font-size: 0.9em;
  color: #e74c3c;
}

/* Floating Text Format Toolbar */
.floating-text-format-popup {
  display: flex;
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  padding: 8px;
  border-radius: 12px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1000;
  opacity: 0;
  transform: translate(-10000px, -10000px);
  transition: opacity 0.3s ease;
  border: 2px solid #34495e;
}

.popup-item {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border: none;
  background: transparent;
  border-radius: 6px;
  cursor: pointer;
  color: #ecf0f1;
  transition: all 0.2s ease;
}

.popup-item:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
}

.popup-item.active {
  background: #3498db;
  color: white;
  box-shadow: 0 2px 8px rgba(52, 152, 219, 0.4);
}

/* Link Editor */
.link-editor {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1000;
  background: white;
  border: 2px solid #bdc3c7;
  border-radius: 12px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  padding: 12px;
  min-width: 320px;
}

.link-input-wrapper {
  display: flex;
  align-items: center;
  gap: 8px;
}

.link-input {
  flex: 1;
  padding: 10px 14px;
  border: 2px solid #bdc3c7;
  border-radius: 6px;
  font-size: 14px;
  transition: all 0.2s ease;
}

.link-input:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.link-preview {
  display: flex;
  align-items: center;
  gap: 8px;
}

.link-url {
  flex: 1;
  color: #3498db;
  text-decoration: none;
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 220px;
  font-weight: 500;
}

.link-url:hover {
  text-decoration: underline;
}

.link-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  background: transparent;
  border-radius: 6px;
  cursor: pointer;
  color: #7f8c8d;
  transition: all 0.2s ease;
}

.link-btn:hover {
  background: #ecf0f1;
  color: #2c3e50;
  transform: translateY(-1px);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .editor-split-view {
    grid-template-columns: 1fr;
  }

  .editor-panel {
    border-right: none;
    border-bottom: 2px solid #e9ecef;
  }

  .preview-panel {
    max-height: 400px;
  }
}

@media (max-width: 768px) {
  .app {
    padding: 0.5rem;
  }

  .header h1 {
    font-size: 2rem;
  }

  .toolbar {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
    padding: 16px;
  }

  .toolbar-group {
    border-right: none;
    border-bottom: 1px solid #ced4da;
    padding-right: 0;
    padding-bottom: 12px;
    margin-right: 0;
    margin-bottom: 12px;
    width: 100%;
    justify-content: flex-start;
  }

  .toolbar-group:last-child {
    border-bottom: none;
    padding-bottom: 0;
    margin-bottom: 0;
  }

  .editor-input,
  .html-preview {
    padding: 16px;
  }

  .editor-placeholder {
    top: 16px;
    left: 16px;
  }

  .editor-footer {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .footer-stats {
    justify-content: center;
  }
}

/* Animation */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.editor-container {
  animation: fadeIn 0.5s ease-out;
}

/* Print Styles */
@media print {
  .toolbar,
  .editor-footer,
  .floating-text-format-popup,
  .link-editor {
    display: none !important;
  }

  .editor-split-view {
    grid-template-columns: 1fr;
  }

  .editor-panel {
    display: none;
  }

  .preview-panel {
    border: none;
  }

  .panel-header {
    display: none;
  }

  .html-preview {
    padding: 0;
    background: white;
  }
}

/* Scrollbar Styling */
.editor-content::-webkit-scrollbar,
.preview-content::-webkit-scrollbar {
  width: 8px;
}

.editor-content::-webkit-scrollbar-track,
.preview-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.editor-content::-webkit-scrollbar-thumb,
.preview-content::-webkit-scrollbar-thumb {
  background: #bdc3c7;
  border-radius: 4px;
}

.editor-content::-webkit-scrollbar-thumb:hover,
.preview-content::-webkit-scrollbar-thumb:hover {
  background: #95a5a6;
}
